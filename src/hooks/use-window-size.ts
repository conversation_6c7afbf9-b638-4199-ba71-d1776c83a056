'use client'
import { useEffect, useState } from 'react'

// Custom debounce function
function debounce<T extends (...args: any[]) => void>(func: T, wait: number) {
  let timeout: ReturnType<typeof setTimeout>

  return function debounced(...args: Parameters<T>) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

interface WindowSize {
  width: number
  height: number
}

export function useWindowSize(debounceTime: number = 100): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: 1200,
    height: 800,
  })

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    const debouncedHandleResize = debounce(handleResize, debounceTime)

    window.addEventListener('resize', debouncedHandleResize)

    handleResize()

    return () => window.removeEventListener('resize', debouncedHandleResize)
  }, [debounceTime])

  return windowSize
}
