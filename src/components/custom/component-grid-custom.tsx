'use client'

import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

import { MainCard } from '@/components/cards/main-card'
import { useWindowSize } from '@/hooks/use-window-size'
import { ComponentWithCategory } from '@/types/custom'

function ComponentGridCustom({
  components,
  isTemplate = false,
}: {
  components: ComponentWithCategory[]
  isTemplate?: boolean
}) {
  const { width } = useWindowSize()
  const [columns, setColumns] = useState<ComponentWithCategory[][]>([])

  useEffect(() => {
    // Determine number of columns based on screen width
    const columnCount = width < 640 ? 1 : width < 1024 ? 2 : 3

    // Create columns array
    const cols: ComponentWithCategory[][] = Array.from(
      { length: columnCount },
      () => []
    )

    // Distribute items across columns
    components?.forEach((item, index) => {
      cols[index % columnCount].push(item)
    })

    setColumns(cols)
  }, [components, width])

  if (!columns.length) return null

  return (
    <div className="container mx-auto px-0 ">
      <motion.div
        className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 lg:gap-5"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {columns.map((column, columnIndex) => (
          <div key={columnIndex} className="flex flex-col gap-4 lg:gap-5">
            {column.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: columnIndex * 0.1 }}
              >
                <MainCard component={item} isTemplate={isTemplate} />
              </motion.div>
            ))}
          </div>
        ))}
      </motion.div>
    </div>
  )
}

export default ComponentGridCustom
